import { Injectable } from '@nestjs/common';
import { In } from 'typeorm';
import {
    Order,
    OrderItemPriceCalculationStrategy,
    PriceCalculationResult,
    ProductVariant,
    RequestContext,
    TransactionalConnection,
    Logger,
} from '@vendure/core';
import { Decal } from '../entities/decal.entity';
import { SelectedDecal } from '../types';

/**
 * @description
 * A price calculation strategy that adds the cost of selected decals to the base product price.
 * This strategy implements the OrderItemPriceCalculationStrategy interface to automatically
 * calculate the total price including decal costs and color option modifiers.
 *
 * @example
 * ```typescript
 * // Configure in vendure-config.ts
 * orderOptions: {
 *     orderItemPriceCalculationStrategy: DecalPriceCalculationStrategy,
 * }
 * ```
 */
@Injectable()
export class DecalPriceCalculationStrategy implements OrderItemPriceCalculationStrategy {
    private static readonly loggerCtx = 'DecalPriceCalculationStrategy';

    constructor(private connection: TransactionalConnection) {}

    /**
     * @description
     * Calculates the unit price for an order line including the base product price
     * and any additional costs from selected decals and their color options.
     *
     * @param ctx - The request context
     * @param productVariant - The product variant being priced
     * @param orderLineCustomFields - Custom fields containing selected decals data
     * @param order - The order (unused but required by interface)
     * @param quantity - The quantity (unused but required by interface)
     * @returns Promise resolving to the calculated price result
     */
    async calculateUnitPrice(
        ctx: RequestContext,
        productVariant: ProductVariant,
        orderLineCustomFields: { [key: string]: any },
        order: Order,
        quantity: number,
    ): Promise<PriceCalculationResult> {
        // Start with the base product variant price
        let basePrice = productVariant.price;

        // Validate input parameters
        if (!productVariant || typeof productVariant.price !== 'number') {
            Logger.warn('Invalid product variant or price provided', DecalPriceCalculationStrategy.loggerCtx);
            return {
                price: 0,
                priceIncludesTax: false,
            };
        }

        // Get selected decals from custom fields
        const selectedDecals = this.parseSelectedDecals(orderLineCustomFields.selectedDecals);

        if (selectedDecals.length === 0) {
            // No decals selected, return base price
            return {
                price: basePrice,
                priceIncludesTax: productVariant.listPriceIncludesTax,
            };
        }
        
        try {
            // Calculate additional cost from decals
            const decalsCost = await this.calculateDecalsCost(ctx, selectedDecals);
            basePrice += decalsCost;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : undefined;
            Logger.error(
                `Failed to calculate decals cost: ${errorMessage}`,
                DecalPriceCalculationStrategy.loggerCtx,
                errorStack,
            );
            // Continue with base price if decal calculation fails
        }

        return {
            price: basePrice,
            priceIncludesTax: productVariant.listPriceIncludesTax,
        };
    }

    /**
     * @description
     * Parses selected decals from custom field data, handling both string and object formats.
     *
     * @param selectedDecalsData - The selected decals data from custom fields
     * @returns Array of parsed selected decals
     */
    private parseSelectedDecals(selectedDecalsData: any): SelectedDecal[] {
        if (!selectedDecalsData) {
            return [];
        }

        try {
            return typeof selectedDecalsData === 'string'
                ? JSON.parse(selectedDecalsData)
                : selectedDecalsData;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            Logger.warn(
                `Failed to parse selected decals data: ${errorMessage}`,
                DecalPriceCalculationStrategy.loggerCtx,
            );
            return [];
        }
    }

    /**
     * @description
     * Calculates the total cost of selected decals including color option modifiers.
     *
     * @param ctx - The request context
     * @param selectedDecals - Array of selected decals with their options
     * @returns Promise resolving to the total decals cost
     */
    private async calculateDecalsCost(ctx: RequestContext, selectedDecals: SelectedDecal[]): Promise<number> {
        if (selectedDecals.length === 0) {
            return 0;
        }

        // Get all decal IDs to query at once
        const decalIds = selectedDecals.map((sd: SelectedDecal) => sd.decalId);

        if (decalIds.length === 0) {
            return 0;
        }

        // Use the new TypeORM pattern instead of deprecated findByIds
        const decals = await this.connection
            .getRepository(ctx, Decal)
            .find({
                where: { id: In(decalIds) },
                select: ['id', 'price', 'enabled'], // Only select needed fields for performance
            });

        // Create a map for quick lookup
        const decalMap = new Map(decals.map(d => [d.id.toString(), d]));

        let totalDecalsCost = 0;

        // Calculate total decals cost
        for (const selectedDecal of selectedDecals) {
            const decal = decalMap.get(selectedDecal.decalId.toString());
            if (decal && decal.enabled) {
                // Add base decal price (already in smallest currency unit)
                totalDecalsCost += decal.price;

                // Add color option price modifier if applicable
                if (selectedDecal.colorOption && typeof selectedDecal.colorOption.priceModifier === 'number') {
                    totalDecalsCost += selectedDecal.colorOption.priceModifier;
                }
            }
        }

        return totalDecalsCost;
    }
}