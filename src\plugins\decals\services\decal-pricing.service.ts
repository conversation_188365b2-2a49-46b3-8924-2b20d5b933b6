import { Injectable } from '@nestjs/common';
import { In } from 'typeorm';
import { RequestContext, TransactionalConnection, Logger } from '@vendure/core';
import { Decal } from '../entities/decal.entity';
import { SelectedDecal } from '../types';

/**
 * @description
 * Service for calculating decal pricing and handling decal-related price operations.
 * This service provides utilities for calculating decal costs, parsing decal data,
 * and computing total item prices including decal modifiers.
 */
@Injectable()
export class DecalPricingService {
    private static readonly loggerCtx = 'DecalPricingService';

    constructor(private connection: TransactionalConnection) {}

    /**
     * @description
     * Calculate the total price for selected decals including color option modifiers.
     *
     * @param ctx - The request context
     * @param selectedDecals - Array of selected decals with their options
     * @returns Promise resolving to the total decal price
     */
    async calculateDecalPrice(
        ctx: RequestContext,
        selectedDecals: SelectedDecal[]
    ): Promise<number> {
        if (!selectedDecals || selectedDecals.length === 0) {
            return 0;
        }

        try {
            let totalDecalPrice = 0;
            const decalIds = selectedDecals.map((sd: SelectedDecal) => sd.decalId);

            if (decalIds.length > 0) {
                // Use the new TypeORM pattern instead of deprecated findByIds
                const decals = await this.connection
                    .getRepository(ctx, Decal)
                    .find({
                        where: { id: In(decalIds) },
                        select: ['id', 'price', 'enabled'], // Only select needed fields for performance
                    });

                const decalMap = new Map(decals.map(d => [d.id.toString(), d]));

                for (const selectedDecal of selectedDecals) {
                    const decal = decalMap.get(selectedDecal.decalId.toString());
                    if (decal && decal.enabled) {
                        // Add base decal price (already in smallest currency unit)
                        totalDecalPrice += decal.price;

                        // Add color option price modifier if applicable
                        if (selectedDecal.colorOption && typeof selectedDecal.colorOption.priceModifier === 'number') {
                            totalDecalPrice += selectedDecal.colorOption.priceModifier;
                        }
                    }
                }
            }

            return totalDecalPrice;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            Logger.error(
                `Failed to calculate decal price: ${errorMessage}`,
                DecalPricingService.loggerCtx,
            );
            return 0; // Return 0 on error to prevent pricing failures
        }
    }

    /**
     * @description
     * Parse selected decals from custom field data, handling both string and object formats.
     *
     * @param selectedDecalsString - The selected decals data (string or object)
     * @returns Array of parsed selected decals
     */
    parseSelectedDecals(selectedDecalsString: string | SelectedDecal[]): SelectedDecal[] {
        if (!selectedDecalsString) {
            return [];
        }

        try {
            return typeof selectedDecalsString === 'string'
                ? JSON.parse(selectedDecalsString)
                : selectedDecalsString;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            Logger.warn(
                `Failed to parse selected decals data: ${errorMessage}`,
                DecalPricingService.loggerCtx,
            );
            return [];
        }
    }

    /**
     * @description
     * Calculate total item price including base price and decals.
     *
     * @param ctx - The request context
     * @param basePrice - The base price of the item
     * @param selectedDecalsString - The selected decals data (string or object)
     * @returns Promise resolving to the total item price
     */
    async calculateTotalItemPrice(
        ctx: RequestContext,
        basePrice: number,
        selectedDecalsString: string | SelectedDecal[]
    ): Promise<number> {
        if (typeof basePrice !== 'number' || basePrice < 0) {
            Logger.warn(
                `Invalid base price provided: ${basePrice}`,
                DecalPricingService.loggerCtx,
            );
            return 0;
        }

        try {
            const selectedDecals = this.parseSelectedDecals(selectedDecalsString);
            const decalPrice = await this.calculateDecalPrice(ctx, selectedDecals);
            return basePrice + decalPrice;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            Logger.error(
                `Failed to calculate total item price: ${errorMessage}`,
                DecalPricingService.loggerCtx,
            );
            return basePrice; // Return base price on error
        }
    }
}